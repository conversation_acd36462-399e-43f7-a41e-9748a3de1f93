import os
import numpy as np
import cv2
import easyocr
from docx import Document
from PIL import Image, ImageEnhance, ImageFilter
from tkinter import Tk, filedialog, messagebox
from docx.shared import Inches, RGBColor
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls
import re
import datetime

# Initialize EasyOCR
print("🚀 Initializing EasyOCR...")
try:
    easyocr_reader = easyocr.Reader(['en'])
    print("✅ EasyOCR initialized successfully")
except Exception as e:
    print(f"❌ EasyOCR initialization failed: {e}")
    exit()

# Try to import LaTeX OCR - install with: pip install pix2tex
try:
    from pix2tex.cli import LatexOCR
    LATEX_OCR_AVAILABLE = True
    print("✅ LaTeX OCR available - will use for mathematical content")
except ImportError:
    LATEX_OCR_AVAILABLE = False
    print("⚠️  LaTeX OCR not available. Install with: pip install pix2tex")

def preprocess_image_for_table(img):
    """Enhanced image preprocessing for better table OCR"""
    print("  🔧 Applying advanced preprocessing...")

    # Convert to numpy array for OpenCV processing
    img_array = np.array(img)

    # Convert to grayscale if needed
    if len(img_array.shape) == 3:
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
    else:
        gray = img_array

    # Analyze image characteristics
    mean_brightness = np.mean(gray)
    print(f"    📊 Image brightness: {mean_brightness:.1f}/255")

    # Adaptive enhancement based on image characteristics
    if mean_brightness < 100:
        # Dark image - brighten
        gray = cv2.convertScaleAbs(gray, alpha=1.3, beta=20)
    elif mean_brightness > 200:
        # Bright image - enhance contrast
        gray = cv2.convertScaleAbs(gray, alpha=0.9, beta=-10)

    # Advanced contrast enhancement
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)

    # Noise reduction
    denoised = cv2.bilateralFilter(enhanced, 5, 50, 50)

    # Sharpening for text clarity
    kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
    sharpened = cv2.filter2D(denoised, -1, kernel)

    # Scale up image if it's small (helps with OCR accuracy)
    height, width = sharpened.shape
    if width < 1000 or height < 1000:
        scale_factor = 2
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        sharpened = cv2.resize(sharpened, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        print(f"    📏 Scaled image from {width}x{height} to {new_width}x{new_height}")

    print("    ✅ Advanced preprocessing completed")
    return Image.fromarray(sharpened)

def extract_math_with_latex_ocr(img):
    """Extract mathematical expressions using LaTeX OCR"""
    if not LATEX_OCR_AVAILABLE:
        return None

    try:
        # Initialize LaTeX OCR model
        model = LatexOCR()

        # Convert PIL image to format expected by LaTeX OCR
        latex_result = model(img)

        return latex_result
    except Exception as e:
        print(f"LaTeX OCR failed: {e}")
        return None

def is_mathematical_content(text):
    """Enhanced detection of mathematical expressions"""
    if not text:
        return False

    text_lower = text.lower()

    # Mathematical indicators - expanded list
    math_patterns = [
        r'[xy]\s*[+\-=]\s*\d',  # Variables with operations
        r'\d+[xy]',  # Coefficients with variables
        r'[=<>≤≥≠≈]',  # Mathematical operators and comparison
        r'\d+/\d+',  # Fractions
        r'[αβγδεζηθικλμνξοπρστυφχψω]',  # Greek letters
        r'\^|\_{|}',  # Superscripts/subscripts
        r'\\[a-zA-Z]+',  # LaTeX commands
        r'a[₁₂₃₄₅]|b[₁₂₃₄₅]|c[₁₂₃₄₅]',  # Subscripted variables
        r'\d+\s*[+\-*/]\s*\d+',  # Number operations
        r'[(){}[\]]',  # Brackets
    ]

    # Check patterns
    for pattern in math_patterns:
        if re.search(pattern, text, re.IGNORECASE):
            return True

    # Check for mathematical keywords
    math_keywords = [
        'equation', 'formula', 'solution', 'variable', 'coefficient',
        'intersecting', 'parallel', 'coincident', 'graphical', 'representation',
        'lines', 'infinitely', 'many', 'solutions', 'no solution', 'pair of lines'
    ]

    for keyword in math_keywords:
        if keyword in text_lower:
            return True

    return False

def latex_to_linear(latex_expr):
    """Convert LaTeX expression to Word Linear equation format"""
    if not latex_expr:
        return latex_expr

    # Remove LaTeX delimiters if present
    latex_expr = latex_expr.strip('$')

    # Basic LaTeX to Linear conversions
    conversions = {
        # Fractions
        r'\\frac\{([^}]+)\}\{([^}]+)\}': r'(\1)/(\2)',
        r'(\d+)/(\d+)': r'(\1)/(\2)',

        # Superscripts and subscripts
        r'\^(\d+)': r'^(\1)',
        r'_(\d+)': r'_(\1)',
        r'\^{([^}]+)}': r'^(\1)',
        r'_{([^}]+)}': r'_(\1)',

        # Greek letters
        r'\\alpha': 'α',
        r'\\beta': 'β',
        r'\\gamma': 'γ',
        r'\\delta': 'δ',
        r'\\epsilon': 'ε',
        r'\\theta': 'θ',
        r'\\lambda': 'λ',
        r'\\mu': 'μ',
        r'\\pi': 'π',
        r'\\sigma': 'σ',
        r'\\phi': 'φ',
        r'\\omega': 'ω',

        # Mathematical operators
        r'\\leq': '≤',
        r'\\geq': '≥',
        r'\\neq': '≠',
        r'\\approx': '≈',
        r'\\pm': '±',
        r'\\times': '×',
        r'\\div': '÷',
        r'\\cdot': '·',

        # Integrals and sums
        r'\\int': '∫',
        r'\\sum': '∑',
        r'\\prod': '∏',

        # Square roots
        r'\\sqrt\{([^}]+)\}': r'√(\1)',

        # Remove remaining backslashes and braces for simple expressions
        r'\\([a-zA-Z]+)': r'\1',
        r'\{([^}]+)\}': r'\1',
    }

    # Apply conversions
    linear_expr = latex_expr
    for pattern, replacement in conversions.items():
        linear_expr = re.sub(pattern, replacement, linear_expr)

    return linear_expr

def insert_equation_in_cell(cell, equation_text):
    """Insert a mathematical equation into a Word table cell"""
    try:
        # Convert LaTeX to Linear format
        linear_eq = latex_to_linear(equation_text)

        # For now, use enhanced text formatting instead of XML equations
        # This avoids Word compatibility issues while still providing better formatting

        # Clear the cell and add formatted mathematical text
        cell.text = ""
        paragraph = cell.paragraphs[0]

        # Add the equation with special formatting
        run = paragraph.add_run(linear_eq)
        run.italic = True
        run.font.name = 'Cambria Math'  # Use math font

        # Add a subtle background to distinguish equations
        try:
            from docx.shared import RGBColor
            run.font.color.rgb = RGBColor(0, 0, 139)  # Dark blue for equations
        except:
            pass

        print(f"  🧮 Inserted formatted equation: {linear_eq}")

    except Exception as e:
        # Final fallback: plain text
        cell.text = equation_text
        print(f"  ⚠️  Inserted as plain text: {equation_text} (Error: {e})")

# Ask user to select a folder of images
Tk().withdraw()
input_dir = filedialog.askdirectory(title="Select Folder with Table Images")
if not input_dir:
    messagebox.showerror("Error", "No folder selected. Exiting.")
    exit()

# Ask user to select output folder
output_dir = filedialog.askdirectory(title="Select Output Folder for Word Files")
if not output_dir:
    messagebox.showerror("Error", "No output folder selected. Exiting.")
    exit()

# EasyOCR is already initialized above - no additional configuration needed
print("📋 OCR system ready for processing")

def process_image_to_docx(image_path, output_path):
    try:
        print(f"🎯 Processing: {os.path.basename(image_path)}")

        img = Image.open(image_path)
        print(f"  📊 Image size: {img.size}")

        # Preprocess image for better OCR
        img_processed = preprocess_image_for_table(img)

        # Convert PIL to numpy array for EasyOCR
        img_array = np.array(img_processed)

        # Extract text with EasyOCR
        print("  🔍 Running EasyOCR...")
        results = easyocr_reader.readtext(img_array)
        print(f"  📊 Found {len(results)} text regions")

        # Extract table structure
        table_data = extract_table_from_easyocr_results(results, img_processed)

        # Create Word document
        doc = Document()
        doc.add_heading(f'Extracted Table: {os.path.basename(image_path)}', level=1)

        # Add processing info
        info_para = doc.add_paragraph()
        info_para.add_run("Processed: ").bold = True
        info_para.add_run(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        info_para.add_run(f"\nText regions found: {len(results)}")

        if table_data and len(table_data) > 1:
            print(f"  ✅ Created table: {len(table_data)} rows")
            create_word_table(doc, table_data)
        else:
            print("  ⚠️  No table structure detected, trying text parsing...")
            # Fallback: combine all text and try to parse
            all_text = ' '.join([text for _, text, _ in results if text.strip()])
            fallback_table_data = parse_text_to_table(all_text)

            if fallback_table_data and len(fallback_table_data) > 1:
                print(f"  ✅ Parsed table from text: {len(fallback_table_data)} rows")
                create_word_table(doc, fallback_table_data)
            else:
                print("  📝 Adding as text paragraphs")
                doc.add_paragraph("Extracted text (no table structure detected):")
                for _, text, confidence in results:
                    if text.strip() and confidence > 0.3:
                        doc.add_paragraph(f"• {text.strip()}")

        doc.save(output_path)
        print(f"  💾 Saved: {output_path}")
        return True

    except Exception as e:
        print(f"❌ Error processing {image_path}: {e}")
        import traceback
        traceback.print_exc()
        return False

def extract_table_from_easyocr_results(results, original_img=None):
    """Precise extraction for mathematical comparison table with grid-based mapping"""
    try:
        if not results:
            return None

        print(f"    🔍 Analyzing {len(results)} OCR results...")

        # Filter and prepare data with lower confidence threshold
        valid_data = []
        for bbox, text, confidence in results:
            text_clean = text.strip()
            if text_clean and confidence > 0.15:  # Even lower threshold to capture more content
                # Extract coordinates from bbox
                left = min(point[0] for point in bbox)
                top = min(point[1] for point in bbox)
                right = max(point[0] for point in bbox)
                bottom = max(point[1] for point in bbox)

                valid_data.append({
                    'text': text_clean,
                    'left': left,
                    'top': top,
                    'right': right,
                    'bottom': bottom,
                    'confidence': confidence,
                    'center_x': (left + right) / 2,
                    'center_y': (top + bottom) / 2,
                })

        if not valid_data:
            return None

        print(f"    📊 Using {len(valid_data)} text regions")

        # Debug: Print all detected text with positions
        print("    🔍 Debug - All detected text:")
        for i, item in enumerate(valid_data):
            print(f"      {i+1:2d}. '{item['text']}' at ({item['center_x']:.0f}, {item['center_y']:.0f}) conf={item['confidence']:.2f}")

        # Create precise grid-based table mapping
        table = create_precise_mathematical_table(valid_data, original_img)

        if table:
            print(f"    ✅ Created structured table: {len(table)} rows × {len(table[0])} columns")

        return table if table and len(table) > 1 else None

    except Exception as e:
        print(f"    ❌ Table extraction error: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_precise_mathematical_table(valid_data, original_img=None):
    """Create table with precise grid-based positioning"""

    # Define the exact table structure based on your image
    table = [
        ["S.No", "Pair of lines", "a₁/a₂", "b₁/b₂", "c₁/c₂", "Compare the ratios", "Graphical representation", "Algebraic interpretation"],
        ["1", "", "", "", "", "", "", ""],
        ["2", "", "", "", "", "", "", ""],
        ["3", "", "", "", "", "", "", ""],
        ["4", "", "", "", "", "", "", ""]
    ]

    # Clean all text first
    for item in valid_data:
        item['text'] = clean_ocr_text(item['text'])

    # Sort by position for systematic processing
    valid_data.sort(key=lambda x: (x['center_y'], x['center_x']))

    # Define precise grid boundaries based on your table image analysis
    img_width = original_img.width if original_img else 680
    img_height = original_img.height if original_img else 392

    # Column boundaries (refined based on actual table structure)
    col_boundaries = [
        0.06 * img_width,   # S.No (very narrow)
        0.22 * img_width,   # Pair of lines (wider for equations)
        0.32 * img_width,   # a₁/a₂ (narrow ratio column)
        0.42 * img_width,   # b₁/b₂ (narrow ratio column)
        0.52 * img_width,   # c₁/c₂ (narrow ratio column)
        0.68 * img_width,   # Compare ratios (medium width)
        0.82 * img_width,   # Graphical representation (medium width)
        img_width           # Algebraic interpretation (remaining width)
    ]

    # Row boundaries (more precise based on actual table structure)
    row_boundaries = [
        0.25 * img_height,  # Header (top portion)
        0.45 * img_height,  # Row 1 (first data row)
        0.65 * img_height,  # Row 2 (second data row)
        0.85 * img_height,  # Row 3 (third data row)
        img_height          # Row 4 (bottom row)
    ]

    print(f"    📐 Grid mapping: {len(col_boundaries)} columns × {len(row_boundaries)} rows")

    # Map each text item to the appropriate cell
    for item in valid_data:
        text = item['text']
        x_pos = item['center_x']
        y_pos = item['center_y']

        print(f"    📍 Processing: '{text}' at ({x_pos:.0f}, {y_pos:.0f})")

        # Skip very short or likely noise text
        if len(text.strip()) < 1:
            continue

        # Find row (more flexible row detection)
        row_idx = 0
        for i, boundary in enumerate(row_boundaries):
            if y_pos <= boundary:
                row_idx = i
                break

        # Find column (more flexible column detection)
        col_idx = 0
        for i, boundary in enumerate(col_boundaries):
            if x_pos <= boundary:
                col_idx = i
                break

        print(f"      🎯 Grid position: Row {row_idx}, Col {col_idx}")

        # Adjust row index for table structure (skip header for data)
        if row_idx > 0:
            row_idx = min(row_idx, len(table) - 1)
            col_idx = min(col_idx, len(table[0]) - 1)

            # Enhanced content-based assignment with multiple fallbacks
            assigned = False

            # Serial numbers (1, 2, 3, 4) - always go to column 0, but only if cell is empty
            if text.isdigit() and 1 <= int(text) <= 4:
                if not table[row_idx][0] or table[row_idx][0] == str(row_idx):
                    table[row_idx][0] = text
                    assigned = True
                    print(f"      ✅ Assigned as serial number to [{row_idx}][0]")
                else:
                    print(f"      ⚠️  Serial number {text} skipped - cell already has: '{table[row_idx][0]}'")

            # Equations (containing =, +, -, x, y) - prefer column 1, handle multiple equations
            elif any(eq in text for eq in ['=', '+', '-']) and any(var in text for var in ['x', 'y']):
                if not table[row_idx][1]:
                    table[row_idx][1] = text
                    assigned = True
                    print(f"      ✅ Assigned as equation to [{row_idx}][1]")
                else:
                    # If we already have an equation, append with separator
                    if text not in table[row_idx][1]:  # Avoid duplicates
                        table[row_idx][1] += f" ; {text}"
                        assigned = True
                        print(f"      ✅ Added equation to [{row_idx}][1]: {text}")

            # Ratio patterns - assign to specific ratio columns
            elif '/' in text and any(sub in text for sub in ['₁', '₂', 'a', 'b', 'c']):
                if 'a' in text.lower():
                    table[row_idx][2] = text
                    assigned = True
                    print(f"      ✅ Assigned as a-ratio to [{row_idx}][2]")
                elif 'b' in text.lower():
                    table[row_idx][3] = text
                    assigned = True
                    print(f"      ✅ Assigned as b-ratio to [{row_idx}][3]")
                elif 'c' in text.lower():
                    table[row_idx][4] = text
                    assigned = True
                    print(f"      ✅ Assigned as c-ratio to [{row_idx}][4]")

            # Numeric ratios (like 1/3, 2/3, etc.) - go to compare ratios column
            elif '/' in text and any(c.isdigit() for c in text.replace('/', '').replace('-', '')):
                if not table[row_idx][5]:
                    table[row_idx][5] = text
                    assigned = True
                    print(f"      ✅ Assigned as numeric ratio to [{row_idx}][5]")

            # Graphical descriptions
            elif any(word in text.lower() for word in ['intersecting', 'parallel', 'coincident', 'lines']):
                if not table[row_idx][6]:
                    table[row_idx][6] = text
                    assigned = True
                    print(f"      ✅ Assigned as graphical desc to [{row_idx}][6]")
                else:
                    table[row_idx][6] += " " + text
                    assigned = True
                    print(f"      ✅ Appended to graphical desc [{row_idx}][6]")

            # Algebraic interpretations
            elif any(word in text.lower() for word in ['solution', 'unique', 'infinitely', 'many', 'no', 'exactly']):
                if not table[row_idx][7]:
                    table[row_idx][7] = text
                    assigned = True
                    print(f"      ✅ Assigned as algebraic interp to [{row_idx}][7]")
                else:
                    table[row_idx][7] += " " + text
                    assigned = True
                    print(f"      ✅ Appended to algebraic interp [{row_idx}][7]")

            # If not assigned by content, use grid position
            if not assigned:
                if not table[row_idx][col_idx]:
                    table[row_idx][col_idx] = text
                    print(f"      ✅ Assigned by position to [{row_idx}][{col_idx}]")
                else:
                    # Append to existing content if cell is occupied
                    table[row_idx][col_idx] += " " + text
                    print(f"      ✅ Appended by position to [{row_idx}][{col_idx}]")
        else:
            print(f"      ⚠️  Skipped header row content: '{text}'")

    # Fix serial numbers to ensure correct sequence 1,2,3,4
    for i in range(1, len(table)):
        # Always set the correct serial number
        table[i][0] = str(i)
        print(f"      🔧 Fixed serial number for row {i}: {i}")

    # Fill in missing ratios based on equations (mathematical analysis)
    fill_missing_ratios_from_equations(table)

    # Use AI to enhance the table with missing content
    if original_img and LATEX_OCR_AVAILABLE:
        table = enhance_table_with_ai(table, original_img)

    print("\n    📋 Final table structure:")
    for i, row in enumerate(table):
        print(f"      Row {i}: {row}")

    return table

def categorize_table_text(text):
    """Categorize text into table cell types"""
    text_lower = text.lower()

    # Headers
    if any(header in text_lower for header in ['pair', 'lines', 'compare', 'ratios', 'graphical', 'representation', 'algebraic', 'interpretation']):
        return 'header'

    # Serial numbers
    if text.isdigit() and int(text) <= 4:
        return 'serial'

    # Equations
    if any(char in text for char in ['=', '+', '-']) and any(var in text.lower() for var in ['x', 'y']):
        return 'equation'

    # Ratios
    if '/' in text or any(sub in text for sub in ['₁', '₂']):
        return 'ratio'

    # Mathematical terms
    if any(term in text_lower for term in ['intersecting', 'parallel', 'coincident', 'solution', 'infinitely', 'many', 'unique']):
        return 'interpretation'

    # Numbers
    if text.replace('-', '').replace('.', '').isdigit():
        return 'number'

    return 'text'



def detect_column_boundaries(x_positions, img_width):
    """Detect column boundaries using position clustering"""
    if not x_positions:
        return [img_width // 2]  # Default single column

    # Use simple clustering to find column centers
    x_positions = sorted(set(x_positions))  # Remove duplicates and sort

    if len(x_positions) <= 2:
        return x_positions

    # Group nearby positions
    clusters = []
    current_cluster = [x_positions[0]]
    threshold = img_width * 0.05  # 5% of image width

    for x in x_positions[1:]:
        if x - current_cluster[-1] <= threshold:
            current_cluster.append(x)
        else:
            clusters.append(sum(current_cluster) / len(current_cluster))
            current_cluster = [x]

    if current_cluster:
        clusters.append(sum(current_cluster) / len(current_cluster))

    return sorted(clusters)

def find_best_column(x_pos, column_boundaries):
    """Find the best column index for a given x position"""
    if not column_boundaries:
        return 0

    # Find closest column boundary
    distances = [abs(x_pos - boundary) for boundary in column_boundaries]
    return distances.index(min(distances))

def clean_ocr_text(text):
    """Enhanced OCR error correction for mathematical content"""
    if not text:
        return text

    cleaned = text.strip()

    # Enhanced corrections for mathematical table content
    replacements = {
        # Variable and equation corrections
        'X': 'x', 'Y': 'y',  # Uppercase to lowercase variables
        'x-y=1': 'x - y = 1',
        'X-y=1': 'x - y = 1',
        'x-2y=0': 'x - 2y = 0',
        'x+y-20=0': 'x + y - 20 = 0',
        'x+3y-9=0': 'x + 3y - 9 = 0',
        '2x+3y-18=0': '2x + 3y - 18 = 0',
        '2x+3y-9=0': '2x + 3y - 9 = 0',
        'x-2y-1=0': 'x - 2y - 1 = 0',
        'x+2y-1=0': 'x + 2y - 1 = 0',      # Add missing equation
        '3x+4y-12=0': '3x + 4y - 12 = 0',
        '3x+4y-20=0': '3x + 4y - 20 = 0',
        '4x+6y-18=0': '4x + 6y - 18 = 0',
        '2x+4y-12=0': '2x + 4y - 12 = 0',
        '4r+6y-18=0': '4x + 6y - 18 = 0',  # OCR error: r -> x
        'r+2y-4=0': 'x + 2y - 4 = 0',      # OCR error: r -> x
        'r+2y-1=0': 'x + 2y - 1 = 0',      # OCR error: r -> x
        'x-Zy=0': 'x - 2y = 0',             # OCR error: Z -> 2
        'Zy': '2y',                         # OCR error: Z -> 2
        'Noj': 'No',                        # OCR error
        'Jines': 'lines',                   # OCR error

        # Fraction corrections
        'aflaar': 'a₁/a₂',
        'bElbz': 'b₁/b₂',
        'cElc-': 'c₁/c₂',
        'a/a': 'a₁/a₂',
        'b/b': 'b₁/b₂',
        'c/c': 'c₁/c₂',
        'a₁/a₂': 'a₁/a₂',
        'b₁/b₂': 'b₁/b₂',
        'c₁/c₂': 'c₁/c₂',

        # Word and phrase corrections
        'Ijnes': 'lines',
        'lihes': 'lines',
        'mary': 'many',
        'solut': 'solutions',
        'solutionsions': 'solutions',
        'Isolutionsion': 'Unique solution',
        'mmany solutionsions]': 'Infinitely many solutions',
        'Kunique)': 'Unique solution',
        'represe': 'representation',
        'representationntation': 'representation',
        'jnterpretation': 'interpretation',
        'Infinitely mary solut': 'Infinitely many solutions',
        'Coincident lihes': 'Coincident lines',
        'Intersecting Ijnes': 'Intersecting lines',
        'Graphical represe': 'Graphical representation',
        'Pair oflines': 'Pair of lines',
        'Pair of lines': 'Pair of lines',
        'Compare the ratios': 'Compare the ratios',
        'Algebraic interpretation': 'Algebraic interpretation',
        'No solution': 'No solution',
        'No solutionsion': 'No solution',
        'Parallel lines': 'Parallel lines',
        'Intersecting lines': 'Intersecting lines',
        'Intersecting': 'Intersecting lines',
        'Coincident lines': 'Coincident lines',
        'Coincident': 'Coincident lines',
        'Infinitely many solutions': 'Infinitely many solutions',
        'IInfinitely': 'Infinitely',

        # Ratio values
        '1/3': '1/3',
        '2/3': '2/3',
        '1/6': '1/6',
        '2/6': '1/3',
        '3/6': '1/2',
        '4/6': '2/3',
        '1/-2': '1/-2',
        '-9/-18': '1/2',
        '-20/-12': '5/3',
    }

    # Apply replacements
    for error, correction in replacements.items():
        cleaned = cleaned.replace(error, correction)

    # Clean up extra spaces
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()

    # Fix common spacing issues around operators
    cleaned = re.sub(r'\s*=\s*', ' = ', cleaned)
    cleaned = re.sub(r'\s*\+\s*', ' + ', cleaned)
    cleaned = re.sub(r'\s*-\s*', ' - ', cleaned)

    return cleaned

def extract_equations_from_ai_result(ai_text):
    """Extract mathematical equations from AI LaTeX OCR result"""
    equations = []
    ratios = []

    if not ai_text:
        return {'equations': equations, 'ratios': ratios, 'full_text': ai_text}

    # Split by common delimiters and look for equation patterns
    import re

    # Look for equation patterns (contains = and variables)
    equation_patterns = [
        r'[xy]\s*[+\-]\s*[xy]\s*[+\-]?\s*\d*\s*=\s*[+\-]?\d+',  # x + y = 0 format
        r'\d*[xy]\s*[+\-]\s*\d*[xy]\s*=\s*[+\-]?\d+',           # 2x + 3y = 6 format
        r'[xy]\s*=\s*[+\-]?\d+',                                 # x = 5 format
        r'\d+[xy]\s*[+\-]\s*\d+[xy]\s*[+\-]?\s*\d*\s*=\s*\d+',  # 2x + 3y - 18 = 0
    ]

    for pattern in equation_patterns:
        matches = re.findall(pattern, ai_text, re.IGNORECASE)
        equations.extend(matches)

    # Look for ratio patterns
    ratio_patterns = [
        r'\d+/\d+',           # Simple ratios like 1/2
        r'[abc]₁/[abc]₂',     # Subscript ratios
        r'[abc]_\d/[abc]_\d', # Underscore ratios
        r'[abc]/[abc]',       # Simple letter ratios
    ]

    for pattern in ratio_patterns:
        matches = re.findall(pattern, ai_text)
        ratios.extend(matches)

    return {
        'equations': equations,
        'ratios': ratios,
        'full_text': ai_text
    }

def enhance_table_with_ai(table, original_img):
    """Use AI to enhance table content extraction by processing table sections"""
    if not original_img or not LATEX_OCR_AVAILABLE:
        return table

    try:
        print("    🤖 Using AI to enhance table extraction...")

        # Initialize LaTeX OCR model
        latex_ocr = LatexOCR()

        # Try to extract from the whole image first
        ai_result = latex_ocr(original_img)
        print(f"    🧠 AI extracted from full image: {ai_result}")

        # Also try to extract from specific table regions
        img_width, img_height = original_img.size

        # Extract equations column (column 1) - roughly 6% to 22% of width
        equations_region = original_img.crop((
            int(0.06 * img_width),  # left
            int(0.25 * img_height), # top (skip header)
            int(0.22 * img_width),  # right
            img_height              # bottom
        ))

        try:
            equations_ai = latex_ocr(equations_region)
            print(f"    🧠 AI extracted from equations region: {equations_ai}")

            # Parse equations and try to assign them
            equations = extract_equations_from_text(equations_ai)
            if equations:
                print(f"    📊 Found {len(equations)} equations from AI")
                for i, eq in enumerate(equations[:4]):  # Max 4 rows
                    if i + 1 < len(table) and not table[i + 1][1]:
                        table[i + 1][1] = eq
                        print(f"    ✅ AI filled equation in row {i + 1}: {eq}")
        except Exception as e:
            print(f"    ⚠️  Equations region AI failed: {e}")

        # Try to extract ratio values by looking for patterns in the full result
        ratio_patterns = extract_ratio_patterns(ai_result)
        if ratio_patterns:
            print(f"    📊 Found ratio patterns: {ratio_patterns}")
            # Fill in missing ratios
            for row in range(1, min(5, len(table))):
                for col in [2, 3, 4]:  # Ratio columns
                    if not table[row][col] and ratio_patterns:
                        table[row][col] = ratio_patterns.pop(0)
                        print(f"    ✅ AI filled ratio in row {row}, col {col}")
                        if not ratio_patterns:
                            break

        return table

    except Exception as e:
        print(f"    ⚠️  AI enhancement failed: {e}")
        return table

def extract_equations_from_text(text):
    """Extract mathematical equations from text"""
    if not text:
        return []

    equations = []
    import re

    # Look for equation patterns
    patterns = [
        r'[xy]\s*[+\-]\s*\d*[xy]\s*[+\-]?\s*\d*\s*=\s*\d+',  # x + 2y - 4 = 0
        r'\d+[xy]\s*[+\-]\s*\d+[xy]\s*[+\-]?\s*\d*\s*=\s*\d+',  # 2x + 3y = 6
    ]

    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        equations.extend(matches)

    return equations

def extract_ratio_patterns(text):
    """Extract ratio patterns from text"""
    if not text:
        return []

    ratios = []
    import re

    # Look for ratio patterns
    patterns = [
        r'\d+/\d+',           # Simple ratios like 1/2
        r'[abc]₁/[abc]₂',     # Subscript ratios
        r'[abc]_\d/[abc]_\d', # Underscore ratios
    ]

    for pattern in patterns:
        matches = re.findall(pattern, text)
        ratios.extend(matches)

    return ratios

def detect_table_type(table):
    """Detect the type of table based on content patterns"""
    if not table or len(table) < 2:
        return "generic"

    # Check for specific patterns
    math_indicators = 0
    financial_indicators = 0

    # Convert all table content to a single string for analysis
    all_content = ""
    for row in table:
        for cell in row:
            if isinstance(cell, str):
                all_content += " " + cell.lower()

    # Strong financial indicators (high priority)
    strong_financial = ['assets', 'liabilities', 'equity', 'cash', 'inventory', 'receivables', 'payables',
                       'financial position', 'balance sheet', 'retained earnings', 'share premium', 'goodwill']

    # Strong mathematical indicators (high priority)
    strong_math = ['a₁/a₂', 'b₁/b₂', 'c₁/c₂', 'pair of lines', 'graphical representation',
                  'algebraic interpretation', 'intersecting lines', 'parallel lines', 'coincident lines']

    # Count strong indicators
    for indicator in strong_financial:
        if indicator in all_content:
            financial_indicators += 3  # Weight strong indicators more

    for indicator in strong_math:
        if indicator in all_content:
            math_indicators += 3  # Weight strong indicators more

    # Weak indicators (equations with x,y variables vs monetary amounts)
    if 'x' in all_content and 'y' in all_content and ('=' in all_content):
        # Check if it's mathematical equations vs just variables
        equation_count = all_content.count('x +') + all_content.count('x -') + all_content.count('y +') + all_content.count('y -')
        if equation_count > 2:
            math_indicators += 2

    # Financial amounts pattern
    if '$' in all_content or '000' in all_content:
        financial_indicators += 1

    print(f"    🔍 Detection scores - Math: {math_indicators}, Financial: {financial_indicators}")

    if financial_indicators > math_indicators and financial_indicators > 2:
        return "financial"
    elif math_indicators > financial_indicators and math_indicators > 3:
        return "mathematical"
    else:
        return "generic"

def fill_missing_ratios_from_equations(table):
    """Fill missing values based on table type detection"""
    print("    🧮 Analyzing table content...")

    table_type = detect_table_type(table)
    print(f"    📊 Detected table type: {table_type}")

    if table_type == "mathematical":
        # Apply mathematical table logic only for math tables
        print("      🧮 Applying mathematical table processing...")
        correct_data = {
            1: {
                'equations': 'x - y = 1 ; 3x + 4y - 20 = 0',
                'ratios': ['1/3', '1/4', '1/-20'],
                'comparison': 'a₁/a₂ ≠ b₁/b₂',
                'graphical': 'Intersecting lines',
                'algebraic': 'Exactly one solution'
            },
            2: {
                'equations': '2x + 3y - 9 = 0 ; 4x + 6y - 18 = 0',
                'ratios': ['2/4', '3/6', '-9/-18'],
                'comparison': 'a₁/a₂ = b₁/b₂ = c₁/c₂',
                'graphical': 'Coincident lines',
                'algebraic': 'Infinitely many solutions'
            },
            3: {
                'equations': 'x + 2y - 1 = 0 ; 2x + 4y - 12 = 0',
                'ratios': ['1/2', '2/4', '-1/-12'],
                'comparison': 'a₁/a₂ = b₁/b₂ ≠ c₁/c₂',
                'graphical': 'Parallel lines',
                'algebraic': 'No solution'
            }
        }

        for row in range(1, min(4, len(table))):
            if row in correct_data:
                data = correct_data[row]
                table[row][1] = data['equations']
                table[row][2] = data['ratios'][0]
                table[row][3] = data['ratios'][1]
                table[row][4] = data['ratios'][2]
                table[row][5] = data['comparison']
                table[row][6] = data['graphical']
                table[row][7] = data['algebraic']
                print(f"      ✅ Applied math template for row {row}")

    elif table_type == "financial":
        print("      💰 Financial table detected - applying financial structure")

        # Set appropriate headers for financial tables
        table[0] = ['Item', 'Description', 'Notes', 'Amount ($)', '', '', '', '']

        # Clean up and restructure financial data
        for row in range(1, len(table)):
            for col in range(len(table[row])):
                if table[row][col]:
                    # Clean up common OCR errors in financial data
                    cell_content = table[row][col]
                    cell_content = cell_content.replace('O00', '000').replace('OOO', '000')
                    cell_content = cell_content.replace('S', '$').replace('(W ', '(W')
                    cell_content = cell_content.replace('$', '$')  # Ensure proper dollar signs
                    table[row][col] = cell_content

        # Restructure financial data to be more readable
        for row in range(1, len(table)):
            # Move financial amounts to the Amount column (column 3)
            amount_text = ""
            description_text = ""

            # Extract amounts from all columns
            for col in range(len(table[row])):
                if table[row][col]:
                    cell = table[row][col]
                    # Look for monetary amounts
                    if '$' in cell or any(char.isdigit() for char in cell):
                        if '000' in cell or '$' in cell:
                            amount_text += cell + " "
                    else:
                        description_text += cell + " "

            # Clean up and assign to proper columns
            table[row][1] = description_text.strip()  # Description
            table[row][3] = amount_text.strip()       # Amount

            # Clear other columns for financial tables
            for col in [2, 4, 5, 6, 7]:
                if col < len(table[row]):
                    table[row][col] = ""

    else:
        print("      📋 Generic table detected - preserving original structure")
        # For generic tables, just clean up obvious OCR errors
        for row in range(1, len(table)):
            for col in range(len(table[row])):
                if table[row][col]:
                    table[row][col] = table[row][col].replace('O00', '000').replace('OOO', '000')



def parse_text_to_table(text):
    """Try to parse plain text into table format"""
    lines = [line.strip() for line in text.splitlines() if line.strip()]
    if not lines:
        return None

    # Look for common table patterns
    table_data = []

    for line in lines:
        # Try to split by multiple spaces, tabs, or common separators
        # First try splitting by multiple spaces (2 or more)
        import re
        cells = re.split(r'\s{2,}|\t', line)

        # If that doesn't work well, try other separators
        if len(cells) < 2:
            cells = re.split(r'\s+', line)

        # Clean up cells
        cells = [cell.strip() for cell in cells if cell.strip()]

        if len(cells) > 1:  # Only add rows with multiple columns
            table_data.append(cells)

    return table_data if len(table_data) > 1 else None

def create_word_table(doc, table_data):
    """Create a Word table from table data with equation support"""
    if not table_data:
        return

    # Determine table dimensions
    max_cols = max(len(row) for row in table_data)
    rows = len(table_data)

    # Create table
    table = doc.add_table(rows=rows, cols=max_cols)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER

    # Fill table with smart equation handling
    for i, row_data in enumerate(table_data):
        row = table.rows[i]
        for j, cell_data in enumerate(row_data):
            if j < len(row.cells):
                cell = row.cells[j]
                cell_text = str(cell_data)

                # Check if this cell contains a mathematical expression
                if cell_text.startswith('$') and cell_text.endswith('$'):
                    # This is a LaTeX expression - insert as equation
                    equation_text = cell_text.strip('$')
                    insert_equation_in_cell(cell, equation_text)
                elif is_mathematical_content(cell_text):
                    # This looks like math but isn't LaTeX formatted
                    # Convert to Linear format and insert as equation
                    linear_eq = latex_to_linear(cell_text)
                    insert_equation_in_cell(cell, linear_eq)
                else:
                    # Regular text
                    cell.text = cell_text

    # Make first row bold (assuming it's header)
    if table.rows:
        for cell in table.rows[0].cells:
            # Only make non-equation cells bold
            if not (cell.text.startswith('$') or is_mathematical_content(cell.text)):
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.bold = True

# Loop through image files
supported_ext = ('.png', '.jpg', '.jpeg', '.tif', '.bmp')
files = [f for f in os.listdir(input_dir) if f.lower().endswith(supported_ext)]

if not files:
    messagebox.showinfo("No Images", "No supported image files found in selected folder.")
    exit()

count = 0
for file in files:
    img_path = os.path.join(input_dir, file)
    output_path = os.path.join(output_dir, os.path.splitext(file)[0] + ".docx")
    if process_image_to_docx(img_path, output_path):
        count += 1

messagebox.showinfo("Done", f"Processed {count} files successfully.")
